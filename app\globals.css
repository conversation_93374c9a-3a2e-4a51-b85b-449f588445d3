@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
    font-family: var(--font-poppins), sans-serif;
}

body{
    @apply !text-gray-800;
}

.login-wrapper::before {
    content: "";
    @apply fixed bottom-0 left-0 w-[200px] h-[200px] bg-[url('./assets/images/login-shape-1.svg')] bg-no-repeat bg-[length:100%];
}

.login-wrapper::after {
    content: "";
    @apply fixed top-0 right-[120px] w-[200px] h-[200px] bg-[url('./assets/images/login-shape-2.svg')] bg-no-repeat bg-[length:100%];
}

.register-wrapper .left-panel::before {
    content: "";
    @apply fixed bottom-0 left-0 w-[200px] h-[200px] bg-[url('./assets/images/login-shape-1.svg')] bg-no-repeat bg-[length:100%];
}

.register-wrapper .left-panel::after {
    content: "";
    @apply absolute top-0 right-[40px] w-[200px] h-[200px] bg-[url('./assets/images/login-shape-2.svg')] bg-no-repeat bg-[length:100%];
}


@layer base {
    [data-state='checked'] {
        color: #ffffff !important;
    }
}

.dropdown-item-hover {
    @apply px-2 py-1.5 rounded hover:bg-gray-100 hover:text-gray-900 cursor-pointer;
}

.step {
    @apply w-full px-4 py-3 rounded-xl border-2 border-transparent text-gray-900 bg-gray-100 font-medium relative text-center xl:text-left;
}

.step div {
    @apply hidden xl:block;
}

.step:not(.active):not(.completed) span {
    @apply text-gray-600;
}

.step.active {
    @apply border-cyan-700 bg-primary/10 text-primary;
}

.step.completed {
    @apply border-cyan-700 bg-cyan-700 text-white;
}

.step.completed::after {
    content: "";
    @apply absolute top-2 right-2 w-6 h-6 bg-[url('./assets/images/icons/check-completed.svg')] bg-no-repeat bg-[length:100%];
}

footer {
    @apply hidden;
}

body[data-scroll-locked] header, body[data-scroll-locked] .action-footer {
  width: calc(100% - var(--removed-body-scroll-bar-size, 0px));
}

.page-editor-container .tiptap-toolbar {
    @apply !p-4  overflow-hidden !border !border-b !bg-gray-50 !border-solid !border-b-gray-200 rounded-xl;
}
.page-editor-container .simple-editor-content{
    @apply !max-w-full;
}
.page-editor-container .simple-editor-content .tiptap.ProseMirror {
    @apply !px-0;
}   


