"use server"
import { prisma } from '@/lib/prisma';


/**
 * Fetch all unique companies from user_meta table where meta_key is 'company'.
 * Returns array of { id, name }.
 */
export async function get_all_companies() {
    // Find all user_meta entries with meta_key 'company'
    const metas = await prisma.siteMeta.findMany({
        where: { meta_key: 'company' },
        select: { meta_value: true },
        distinct: ['meta_value'],
    });


    // Remove empty/null and map to { id, name }
    return metas
        .filter((m) => m.meta_value)
        .map((m, idx) => ({
            id: idx,
            name: m.meta_value,
        }));
}