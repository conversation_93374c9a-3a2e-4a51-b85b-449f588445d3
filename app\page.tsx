"use client";
import React, { useState } from "react";
import { useRouter } from "next/navigation";
import {
  <PERSON>,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import Logo from "./assets/images/logo-white.svg";
import Image from "next/image";
import { useAuth } from "@/app/context/AuthContext";
import Link from "next/link";

export default function Login() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [remember, setRemember] = useState(false);
  const [error, setError] = useState("");
  const router = useRouter();
  const { login } = useAuth();

  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault();
    setError("");
    const res = await fetch("/api/auth/login", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ email, password }),
    });
    const data = await res.json();
    if (res.ok) {
      await login(data.token);
      router.push("/dashboard");
    } else {
      setError(data.error || "Login failed");
    }
  }

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-primary relative px-4 relative overflow-hidden login-wrapper">
      <div className="flex flex-col items-center mb-16">
        <Image src={Logo} alt="logo" className="max-w-[200px]" />
      </div>

      {/* Card */}
      <Card className="relative bg-white w-full max-w-[500px] mx-auto rounded-2xl shadow-lg border-none px-4 py-6 sm:px-6 sm:py-8 md:px-10 md:py-12">
        <form onSubmit={handleSubmit}>
          <CardHeader className="text-center p-0 mb-12">
            <CardTitle className="text-3xl font-600">Welcome Back!</CardTitle>
            <CardDescription className="text-sm text-gray-500">
              Sign In to Continue Managing Your Earnings
            </CardDescription>
          </CardHeader>
          <CardContent className="grid gap-6 p-0">
            <div className="grid gap-2 text-left">
              <Label className="gap-1" htmlFor="email">
                Username or Email <span className="text-red-700">*</span>
              </Label>
              <Input
                id="email"
                type="text"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Username or email"
                required
                autoComplete="username"
              />
            </div>
            <div className="grid gap-2 text-left">
              <Label className="gap-1" htmlFor="password">
                Password<span className="text-red-700">*</span>
              </Label>
              <Input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Enter your password"
                required
                autoComplete="current-password"
              />
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="remember"
                  checked={remember}
                  onCheckedChange={(checked) => setRemember(!!checked)}
                />
                <Label htmlFor="remember" className="text-sm font-normal">
                  Remember me
                </Label>
              </div>
              <a href="#" className="text-sm text-primary hover:underline">
                Forgot Password?
              </a>
            </div>
            {error && (
              <div className="text-red-700 text-sm mt-2 text-center">
                {error}
              </div>
            )}
          </CardContent>
          <CardFooter className="flex flex-col gap-8 mt-8 p-0">
            <Button className="w-full text-white" type="submit">
              Sign In
            </Button>
            <span className="text-sm text-center text-gray-500">
              Don't have an account?{" "}
              <Link
                href="/register"
                className="text-primary font-medium hover:underline"
              >
                Sign Up
              </Link>
            </span>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
}
