"use client";
// Import necessary dependencies from React and Next.js
import React, {
  createContext,
  useContext,
  useEffect,
  useState,
  ReactNode,
} from "react";
import { useRouter } from "next/navigation";
import { jwtDecode } from "jwt-decode";

// Define the structure of our authentication context
interface AuthContextType {
  isLoggedIn: boolean; // Tracks if user is logged in
  user: any; // Stores user information
  loading: boolean; // Tracks loading state
  login: (token: string) => Promise<void>; // Function to handle login
  logout: () => void; // Function to handle logout
  refreshToken: () => Promise<boolean>; // Function to refresh auth token
}

// Create the authentication context with undefined as initial value
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Main authentication provider component
export const AuthProvider = ({ children }: { children: ReactNode }) => {
  // Initialize state variables
  const [isLoggedIn, setIsLoggedIn] = useState(false); // Track login status
  const [user, setUser] = useState<any>(null); // Store user data
  const [loading, setLoading] = useState(true); // Track loading state
  const [isInitialized, setIsInitialized] = useState(false); // Track initialization
  const router = useRouter(); // Next.js router for navigation
  const authRoutes = ["/", "/register"];

  // Function to handle user logout
  const logout = () => {
    setIsLoggedIn(false); // Clear login status
    setUser(null); // Clear user data
    setLoading(false); // Clear loading state
    document.cookie = "token=; Max-Age=0; path=/;"; // Remove auth cookie
    document.cookie = "refreshToken=; Max-Age=0; path=/;"; // Remove refresh cookie
    router.push("/"); // Redirect to home page
  };

  // Function to refresh authentication token
  const refreshToken = async () => {
    // Don't refresh if on home page
    if (window.location.pathname === "/") {
      return false;
    }

    try {
      // Call refresh token API
      const res = await fetch("/api/auth/refresh", {
        method: "POST",
        credentials: "include",
      });
      const data = await res.json();

      // If new token received, login with it
      if (data.token) {
        await login(data.token);
        return true;
      }
      return false;
    } catch {
      return false;
    }
  };

  // Function to handle user login
  const login = async (token: string) => {
    try {
      const decoded: any = jwtDecode(token); // Decode JWT token
      setUser(decoded); // Set user data
      setIsLoggedIn(true); // Set login status
      setLoading(false); // Clear loading state
    } catch (error) {
      console.error("Invalid token:", error);
      logout(); // Logout on invalid token
    }
  };

  // Function to check authentication status
  const checkAuth = async () => {
    // Skip check if already initialized and on login page and register page
    if (authRoutes.includes(window.location.pathname)) {
      setLoading(false); // Clear loading state if already initialized and on login page
      return;
    }

    try {
      // Verify current token
      const res = await fetch("/api/auth/verify", {
        method: "POST",
        credentials: "include",
      });

      const data = await res.json();
      if (res.ok && data.token) {
        await login(data.token); // Login if token valid
        setLoading(false); // Clear loading state if token valid
      } else {
        // Try to refresh token if current one invalid
        const refreshed = await refreshToken();
        if (!refreshed) {
          logout(); // Logout if refresh fails
        } else {
          setLoading(false); // Clear loading state if refresh succeeds
        }
      }
    } catch {
      // Handle errors by attempting token refresh
      const refreshed = await refreshToken();
      if (!refreshed) {
        logout();
      } else {
        setLoading(false); // Clear loading state if refresh succeeds
      }
    } finally {
      setLoading(false); // Clear loading state in all cases
      setIsInitialized(true);
    }
  };

  // Effect to check authentication on initial load
  useEffect(() => {
    if (!isInitialized) {
      checkAuth(); // Check authentication on initial load
    }
  }, [isInitialized]); // Run only when isInitialized changes

  // Effect to set up automatic token refresh
  useEffect(() => {
    if (isLoggedIn && window.location.pathname !== "/") {
      // Refresh token every 5 minutes
      const interval = setInterval(refreshToken, 5 * 60 * 1000);
      return () => clearInterval(interval); // Cleanup on unmount
    }
  }, [isLoggedIn]); // Run only when isLoggedIn changes

  // Provide authentication context to children
  return (
    <AuthContext.Provider
      value={{ isLoggedIn, user, loading, login, logout, refreshToken }}
    >
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use authentication context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
