import { NextResponse } from 'next/server';
import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import { prisma } from '@/lib/prisma';


export async function POST(request: Request) {
  const { email, password } = await request.json();

  if (!email || !password) {
    return NextResponse.json({ error: 'Email and password are required' }, { status: 400 });
  }

  const user = await prisma.user.findUnique({
    where: { email },
    select: {
      id: true,
      password: true,
      email: true,
      nicename: true,
      metas: true,
      role: true
    }
  });

  if (!user) {
    return NextResponse.json({ error: 'User not found' }, { status: 404 });
  }

  const isPasswordValid = await bcrypt.compare(password, user.password);

  if (!isPasswordValid) {
    return NextResponse.json({ error: 'Invalid password' }, { status: 401 });
  }

  // Create access token with longer expiration (7 days for development)
  const accessToken = jwt.sign(
    { userId: user.id, email: user.email, nicename: user.nicename, metas: user.metas, role: user.role },
    process.env.JWT_SECRET || 'your-secret-key',
    { expiresIn: '7d' }
  );

  // Create refresh token (30 days)
  const refreshToken = jwt.sign(
    { userId: user.id, email: user.email, nicename: user.nicename, metas: user.metas, role: user.role },
    process.env.JWT_REFRESH_SECRET || 'your-refresh-secret-key',
    { expiresIn: '30d' }
  );

  // Store refresh token in database
  await prisma.user.update({
    where: { id: user.id },
    data: { refresh_token: refreshToken }
  });

  // Set cookies
  const response = NextResponse.json(
    { success: true, token: accessToken, refreshToken: refreshToken },
    { status: 200 }
  );

  // Set access token cookie
  response.cookies.set('token', accessToken, {
    httpOnly: true,
    secure: process.env.APP_ENV === 'production',
    sameSite: 'strict',
    maxAge: 7 * 24 * 60 * 60 // 7 days
  });

  // Set refresh token cookie
  response.cookies.set('refreshToken', refreshToken, {
    httpOnly: true,
    secure: process.env.APP_ENV === 'production',
    sameSite: 'strict',
    maxAge: 30 * 24 * 60 * 60 // 30 days
  });

  return response;
} 