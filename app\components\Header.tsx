"use client";
import React from "react";
import Link from "next/link";
import { useAuth } from "@/app/context/AuthContext";
import Logo from "../assets/images/logo.svg";
import defaultProfileImg from "../assets/images/avatar.png";
import Image from "next/image";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useRouter } from "next/navigation";

// Helper to get meta value by key
function getMetaValue(metas: any[], key: string) {
  return metas?.find((meta) => meta.meta_key === key)?.meta_value || "";
}

export default function Header() {
  const router = useRouter();
  const { isLoggedIn, user, logout, loading } = useAuth();

  // Get first and last name from user.metas
  const firstName = getMetaValue(user?.metas, "first_name");
  const lastName = getMetaValue(user?.metas, "last_name");
  const displayName = `${firstName} ${lastName}`.trim() || "Wilson Workman";

  if (loading || !isLoggedIn) return null;

  const handleLogout = async () => {
    try {
      await fetch("/api/auth/logout", { method: "POST" });
      logout();

      router.push("/");
    } catch (error) {
      console.error("Logout failed:", error);
    }
  };

  return (
    <header className="bg-white border-b border-gray-300 fixed top-0 left-0 z-50 w-full">
      <nav className="container mx-auto px-4 py-4">
        <div className="flex justify-between items-center">
          <Link href="/" className="text-xl font-bold text-gray-800">
            <Image src={Logo} alt="logo" className="max-w-[200px]" />
          </Link>
          <div className="flex space-x-4">
            <DropdownMenu>
              <DropdownMenuTrigger
                className="text-left flex space-x-3 text-gray-600 hover:text-gray-900 focus:outline-none"
                aria-label="User menu"
              >
                <Image
                  src={defaultProfileImg}
                  alt="User Avatar"
                  width={40}
                  height={40}
                  className="rounded-lg"
                />

                <div className="gap-0 flex flex-col">
                  <h5 className="font-[600]">{displayName}</h5>
                  <span className="text-sm">{user?.role.name}</span>
                </div>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="start" className="w-48 bg-white">
                <DropdownMenuItem asChild>
                  <Link href="/profile" className="dropdown-item-hover">
                    Profile Settings
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/dashboard" className="dropdown-item-hover">
                    Dashboard
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={handleLogout}
                  className="dropdown-item-hover"
                >
                  Logout
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </nav>
    </header>
  );
}
